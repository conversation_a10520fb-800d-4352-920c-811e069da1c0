/**
 * AlertBasicForm 表单重置功能测试
 * 用于验证抽屉关闭后表单数据是否正确重置
 */

import React, { useState } from 'react';
import { Button, Space, Card, Form, App } from 'antd';
import AlertBasicForm from '../components/forms/AlertBasicForm';
import type { TaskAlert } from '../types';

/**
 * 表单重置测试组件
 */
const AlertFormResetTest: React.FC = () => {
  const [form] = Form.useForm();
  const [currentRecord, setCurrentRecord] = useState<TaskAlert | null>(null);
  const [visible, setVisible] = useState(false);

  // 模拟编辑数据
  const mockEditData: TaskAlert = {
    id: 1,
    name: '测试告警名称',
    severity: 'high',
    sql: 'SELECT COUNT(*) FROM test_table WHERE status = "error"',
    alert_type: 'isValue',
    values: ['>=1'],
    create_time: '2024-01-01 00:00:00',
    update_time: '2024-01-01 00:00:00',
  };

  // 模拟新增操作
  const handleAdd = () => {
    setCurrentRecord(null);
    form.resetFields(); // 确保新增时表单是空的
    setVisible(true);
  };

  // 模拟编辑操作
  const handleEdit = () => {
    setCurrentRecord(mockEditData);
    setVisible(true);
  };

  // 模拟关闭抽屉
  const handleClose = () => {
    form.resetFields(); // 关闭时重置表单
    setCurrentRecord(null);
    setVisible(false);
  };

  // 模拟提交
  const handleSubmit = async (values: TaskAlert) => {
    console.log('提交数据:', values);
    App.useApp().message.success(currentRecord ? '更新成功' : '创建成功');
    handleClose();
  };

  // 获取当前表单值
  const getCurrentFormValues = () => {
    const values = form.getFieldsValue();
    console.log('当前表单值:', values);
    return values;
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="AlertBasicForm 表单重置功能测试" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <h3>测试说明：</h3>
            <ul>
              <li>1. 点击"编辑告警"按钮，表单会填充测试数据</li>
              <li>2. 点击"关闭抽屉"按钮，模拟关闭抽屉操作</li>
              <li>3. 再次点击"新增告警"按钮，检查表单是否为空</li>
              <li>4. 点击"获取表单值"可以查看当前表单的值</li>
            </ul>
          </div>

          <Space wrap>
            <Button type="primary" onClick={handleAdd}>
              新增告警
            </Button>
            <Button onClick={handleEdit}>
              编辑告警
            </Button>
            <Button onClick={handleClose} disabled={!visible}>
              关闭抽屉
            </Button>
            <Button onClick={getCurrentFormValues}>
              获取表单值
            </Button>
          </Space>

          <div>
            <strong>当前状态：</strong>
            <ul>
              <li>抽屉可见：{visible ? '是' : '否'}</li>
              <li>编辑模式：{currentRecord ? '是' : '否'}</li>
              <li>当前记录ID：{currentRecord?.id || '无'}</li>
            </ul>
          </div>
        </Space>
      </Card>

      {visible && (
        <Card title={currentRecord ? '编辑告警' : '新增告警'} style={{ border: '2px solid #1890ff' }}>
          <AlertBasicForm
            form={form}
            initialData={currentRecord || undefined}
            onSubmit={handleSubmit}
            onCancel={handleClose}
            onReset={() => {
              form.resetFields();
              console.log('表单已重置');
            }}
            loading={false}
          />
        </Card>
      )}
    </div>
  );
};

export default AlertFormResetTest;
